/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/v1/admin/attendance/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action Attendance Log [Admin]
         * @description Bulk Action attendance (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/attendance": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Attendance [Admin]
         * @description Get list of attendance with pagination and filtering (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: (components["schemas"]["AttendanceLog"] & {
                                userName: string;
                                userEmail: string;
                                worksiteName: string;
                            })[];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create Attendance [Admin]
         * @description Create attendance log for admin (Admin Only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description User ID
                         * @example user_abc123
                         */
                        userId: string;
                        /**
                         * @description Worksite ID
                         * @example site_456xyz
                         */
                        worksiteId: string;
                        /**
                         * @description Type of attendance action
                         * @example CHECK_IN
                         * @enum {string}
                         */
                        type: "CHECK_IN" | "CHECK_OUT" | "BREAK" | "RETURN";
                        /**
                         * Format: date
                         * @description Date of attendance (DATEONLY format)
                         * @example 2025-01-15
                         */
                        logDate: string;
                        /**
                         * @description Time of attendance in HH:mm:ss format
                         * @example 08:30:15
                         */
                        logTime: string;
                        /**
                         * @description Attendance status
                         * @example ON_TIME
                         * @enum {string}
                         */
                        status: "ON_TIME" | "LATE";
                        /**
                         * @description Latitude coordinate of attendance location
                         * @example -6.2088
                         */
                        locationLat: number;
                        /**
                         * @description Longitude coordinate of attendance location
                         * @example 106.8456
                         */
                        locationLong: number;
                        /**
                         * Format: binary
                         * @description Face image file (JPEG, JPG, PNG, GIF, WEBP only, max 5MB)
                         */
                        photo: string;
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceLog"] & {
                                userName: string;
                                userEmail: string;
                                worksiteName: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/attendance-rules": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Attendance Rule [Admin]
         * @description Get list of attendance rules with pagination and filtering (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceRule"][];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New Attendance Rule [Admin]
         * @description Create a new attendance rule (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Work site ID where this attendance rule applies
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        worksiteId: string;
                        /**
                         * @description Latitude coordinate of the work site
                         * @example -6.2088
                         */
                        latitude: number;
                        /**
                         * @description Longitude coordinate of the work site
                         * @example 106.8456
                         */
                        longitude: number;
                        /**
                         * @description Allowed radius in meters for attendance check-in
                         * @example 100
                         */
                        radiusInMeter: number;
                        /**
                         * @description Timezone for the work site
                         * @example Asia/Jakarta
                         * @enum {string}
                         */
                        timezone: "Asia/Jakarta" | "Asia/Pontianak" | "Asia/Makassar" | "Asia/Jayapura";
                        /**
                         * @description Start time for check-in with format HH:mm:ss
                         * @example 06:00:00
                         */
                        checkInStartTime: string;
                        /**
                         * @description End time for check-in with format HH:mm:ss
                         * @example 10:15:00
                         */
                        checkInEndTime: string;
                        /**
                         * @description Tolerance in minutes for check-in with
                         * @example 30
                         */
                        checkInToleranceMinutes: number;
                        /**
                         * @description Start time for check-out with format HH:mm:ss
                         * @example 17:00:00
                         */
                        checkOutStartTime: string;
                        /**
                         * @description End time for check-out with format HH:mm:ss
                         * @example 23:59:00
                         */
                        checkOutEndTime: string;
                        /**
                         * @description Tolerance in minutes for check-out
                         * @example 30
                         */
                        checkOutToleranceMinutes: number;
                        /**
                         * @description Start time for break with format HH:mm:ss
                         * @example 12:00:00
                         */
                        breakStartTime: string;
                        /**
                         * @description End time for break with format HH:mm:ss
                         * @example 13:00:00
                         */
                        breakEndTime: string;
                        /**
                         * @description Tolerance in minutes for break
                         * @example 30
                         */
                        breakToleranceMinutes: number;
                        /**
                         * @description Start time for return with format HH:mm:ss
                         * @example 13:00:00
                         */
                        returnStartTime: string;
                        /**
                         * @description End time for return with format HH:mm:ss
                         * @example 14:00:00
                         */
                        returnEndTime: string;
                        /**
                         * @description Tolerance in minutes for return
                         * @example 30
                         */
                        returnToleranceMinutes: number;
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceRule"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/attendance/{attendanceLogId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attendance Log [Admin]
         * @description Get list of attendance rules with pagination and filtering (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceLog"] & {
                                userName: string;
                                userEmail: string;
                                worksiteName: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update Attendance [Admin]
         * @description Update attendance log for admin (Admin Only)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    attendanceLogId: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description User ID
                         * @example user_abc123
                         */
                        userId?: string;
                        /**
                         * @description Worksite ID
                         * @example site_456xyz
                         */
                        worksiteId?: string;
                        /**
                         * @description Type of attendance action
                         * @example CHECK_IN
                         * @enum {string}
                         */
                        type?: "CHECK_IN" | "CHECK_OUT" | "BREAK" | "RETURN";
                        /**
                         * Format: date
                         * @description Date of attendance (DATEONLY format)
                         * @example 2025-01-15
                         */
                        logDate?: string;
                        /**
                         * @description Time of attendance in HH:mm:ss format
                         * @example 08:30:15
                         */
                        logTime?: string;
                        /**
                         * @description Attendance status
                         * @example ON_TIME
                         * @enum {string}
                         */
                        status?: "ON_TIME" | "LATE";
                        /**
                         * @description Latitude coordinate of attendance location
                         * @example -6.2088
                         */
                        locationLat?: number;
                        /**
                         * @description Longitude coordinate of attendance location
                         * @example 106.8456
                         */
                        locationLong?: number;
                        /**
                         * Format: binary
                         * @description Face image file (JPEG, JPG, PNG, GIF, WEBP only, max 5MB)
                         */
                        photo?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceLog"] & {
                                userName: string;
                                userEmail: string;
                                worksiteName: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/attendance-rules/{attendanceRuleId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Create New Attendance Rule [Admin]
         * @description Create a new attendance rule (Admin only)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    attendanceRuleId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Latitude coordinate of the work site
                         * @example -6.2088
                         */
                        latitude?: number;
                        /**
                         * @description Longitude coordinate of the work site
                         * @example 106.8456
                         */
                        longitude?: number;
                        /**
                         * @description Allowed radius in meters for attendance check-in
                         * @example 100
                         */
                        radiusInMeter?: number;
                        /**
                         * @description Timezone for the work site
                         * @example Asia/Jakarta
                         * @enum {string}
                         */
                        timezone?: "Asia/Jakarta" | "Asia/Pontianak" | "Asia/Makassar" | "Asia/Jayapura";
                        /**
                         * @description Start time for check-in with format HH:mm:ss
                         * @example 06:00:00
                         */
                        checkInStartTime?: string;
                        /**
                         * @description End time for check-in with format HH:mm:ss
                         * @example 10:15:00
                         */
                        checkInEndTime?: string;
                        /**
                         * @description Tolerance in minutes for check-in with
                         * @example 30
                         */
                        checkInToleranceMinutes?: number;
                        /**
                         * @description Start time for check-out with format HH:mm:ss
                         * @example 17:00:00
                         */
                        checkOutStartTime?: string;
                        /**
                         * @description End time for check-out with format HH:mm:ss
                         * @example 23:59:00
                         */
                        checkOutEndTime?: string;
                        /**
                         * @description Tolerance in minutes for check-out
                         * @example 30
                         */
                        checkOutToleranceMinutes?: number;
                        /**
                         * @description Start time for break with format HH:mm:ss
                         * @example 12:00:00
                         */
                        breakStartTime?: string;
                        /**
                         * @description End time for break with format HH:mm:ss
                         * @example 13:00:00
                         */
                        breakEndTime?: string;
                        /**
                         * @description Tolerance in minutes for break
                         * @example 30
                         */
                        breakToleranceMinutes?: number;
                        /**
                         * @description Start time for return with format HH:mm:ss
                         * @example 13:00:00
                         */
                        returnStartTime?: string;
                        /**
                         * @description End time for return with format HH:mm:ss
                         * @example 14:00:00
                         */
                        returnEndTime?: string;
                        /**
                         * @description Tolerance in minutes for return
                         * @example 30
                         */
                        returnToleranceMinutes?: number;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["AttendanceRule"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attendance": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Record Attendance
         * @description Record attendance with face recognition
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * Format: binary
                         * @description Face image file (JPEG, JPG, PNG, GIF, WEBP only, max 5MB)
                         */
                        face: string;
                        /**
                         * @description Type of attendance action
                         * @example CHECK_IN
                         * @enum {string}
                         */
                        action: "CHECK_IN" | "CHECK_OUT" | "BREAK" | "RETURN";
                        /**
                         * @description Latitude coordinate of attendance location
                         * @example -6.2088
                         */
                        latitude: number;
                        /**
                         * @description Longitude coordinate of attendance location
                         * @example 106.8456
                         */
                        longitude: number;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Formatted date in Indonesian locale
                                 * @example Senin, 15 Januari 2025
                                 */
                                date: string;
                                /**
                                 * @description Formatted time in HH:mm:ss:ss format
                                 * @example 08:30:15
                                 */
                                time: string;
                                /**
                                 * @description Status of attendance (ON_TIME, LATE, EARLY)
                                 * @example ON_TIME
                                 */
                                attendanceStatus: string;
                                /**
                                 * @description Face recognition status
                                 * @example Terdeteksi dan sesuai
                                 */
                                faceStatus: string;
                                /**
                                 * @description Location verification status
                                 * @example Sesuai
                                 */
                                locationStatus: string;
                                /**
                                 * @description Type of attendance action
                                 * @example CHECK_IN
                                 */
                                type: string;
                                /**
                                 * @description Distance from work site in meters
                                 * @example 25.5
                                 */
                                locationDistanceMeter: number;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attendance/configurations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attendance Configuration
         * @description Get attendance configuration for current user
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Today's date in formatted string
                                 * @example 15 Jan 2025
                                 */
                                today: string;
                                /** @description Work site location coordinates */
                                location: {
                                    /**
                                     * @description Work site latitude
                                     * @example -6.2088
                                     */
                                    latitude: number;
                                    /**
                                     * @description Work site longitude
                                     * @example 106.8456
                                     */
                                    longitude: number;
                                    /**
                                     * @description Maximum allowed distance in meters for attendance check-in
                                     * @example 250
                                     */
                                    maxDistanceInMeter: number;
                                };
                                /**
                                 * @description Current attendance state - single source of truth for UI logic
                                 * @example NOT_YET_CHECKED_IN
                                 * @enum {string}
                                 */
                                attendanceState: "HOLIDAY" | "ON_LEAVE" | "NON_WORKING_DAY" | "NOT_YET_CHECKED_IN" | "CHECKED_IN" | "ON_BREAK" | "READY_TO_CHECKOUT" | "DONE";
                                /** @description Attendance timestamps for today */
                                timestamps: {
                                    /**
                                     * @description Check-in time if already checked in
                                     * @example 08:30:15
                                     */
                                    checkInAt: string | null;
                                    /**
                                     * @description Break time if on break
                                     * @example null
                                     */
                                    breakAt: string | null;
                                    /**
                                     * @description Return time if returned from break
                                     * @example null
                                     */
                                    returnAt: string | null;
                                    /**
                                     * @description Check-out time if already checked out
                                     * @example null
                                     */
                                    checkOutAt: string | null;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attendance/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attendance History
         * @description Get user's attendance history
         */
        get: {
            parameters: {
                query?: {
                    /** @description Start date for history filter (YYYY-MM-DD) */
                    startDate?: string;
                    /** @description End date for history filter (YYYY-MM-DD) */
                    endDate?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Attendance history grouped by date
                                 * @example {
                                 *       "15 Jan 2025": [
                                 *         {
                                 *           "id": "abc123",
                                 *           "date": "15 Jan 2025",
                                 *           "time": "08:30:15",
                                 *           "status": "ON_TIME",
                                 *           "type": "CHECK_IN"
                                 *         }
                                 *       ]
                                 *     }
                                 */
                                histories: {
                                    [key: string]: {
                                        /**
                                         * @description Attendance log ID
                                         * @example Qah4cccEK0MsspUJ9ywAy
                                         */
                                        id: string;
                                        /**
                                         * @description Formatted date
                                         * @example 15 Jan 2025
                                         */
                                        date: string;
                                        /**
                                         * @description Time in HH:mm:ss:ss format
                                         * @example 08:30:15
                                         */
                                        time: string;
                                        /**
                                         * @description Attendance status
                                         * @example ON_TIME
                                         */
                                        status: string;
                                        /**
                                         * @description Attendance type
                                         * @example CHECK_IN
                                         */
                                        type: string;
                                    }[];
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attendance/register-face-sample": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register Face Sample
         * @description Register 3 face samples for biometric authentication
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * Format: binary
                         * @description First face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)
                         */
                        face1: string;
                        /**
                         * Format: binary
                         * @description Second face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)
                         */
                        face2: string;
                        /**
                         * Format: binary
                         * @description Third face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)
                         */
                        face3: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                isFaceSampleRegistered: boolean;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/auth/forgot-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Forgot Password [Admin]
         * @description Request password reset via email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description Email for password reset */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * Format: email
                         * @description Email address for password reset
                         * @example <EMAIL>
                         */
                        email: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/auth/reset-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset Password [Admin]
         * @description Reset password using token received via email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description Reset password data */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Reset password token received via email
                         * @example abc123def456
                         */
                        token: string;
                        /**
                         * @description New password (minimum 6 characters)
                         * @example newPassword123
                         */
                        newPassword: string;
                        /**
                         * @description Confirm new password
                         * @example newPassword123
                         */
                        newPasswordConfirmation: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * SignIn [Admin]
         * @description Login with email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description User credential, fill with Email/Mobile Number/NIK
                         * @example <EMAIL> | 08123456789 | 123456789123456
                         */
                        credential: string;
                        password: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description JWT access token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                accessToken?: string;
                                /**
                                 * @description JWT refresh token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                refreshToken?: string;
                                /** @description User information */
                                user: {
                                    /**
                                     * @description User ID
                                     * @example Qah4cccEK0MsspUJ9ywAy
                                     */
                                    id: string;
                                    /**
                                     * @description User full name
                                     * @example John Doe
                                     */
                                    name: string;
                                    /**
                                     * Format: email
                                     * @description User email address
                                     * @example <EMAIL>
                                     */
                                    email: string;
                                    /**
                                     * @description User profile image URL
                                     * @example https://example.com/avatar.jpg
                                     */
                                    image: string;
                                    /**
                                     * @description Array of user role
                                     * @example [
                                     *       "Director"
                                     *     ]
                                     */
                                    roles: string[];
                                };
                                /**
                                 * @description Whether user has registered face samples for biometric authentication
                                 * @example true
                                 */
                                isFaceSampleRegistered: boolean;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/forgot-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Forgot Password
         * @description Request password reset via email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description Email for password reset */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * Format: email
                         * @description Email address for password reset
                         * @example <EMAIL>
                         */
                        email: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Me
         * @description Get current logged-in user
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Unique identifier for the user
                                 * @example user_bXn1LnLm8GNqn2OKSE4db
                                 */
                                id: string;
                                /**
                                 * @description Full name of the user
                                 * @example John Doe
                                 */
                                name: string;
                                /**
                                 * Format: email
                                 * @description Email address of the user
                                 * @example <EMAIL>
                                 */
                                email: string;
                                /**
                                 * @description User profile image URL
                                 * @example /uploads/avatars/user-avatar-123.jpg
                                 */
                                image: string;
                                roles: string[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/logout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Logout
         * @description Logout endpoint both for user and admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/refresh-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Refresh Token
         * @description Refresh access token using token refresh
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description New JWT access token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                accessToken: string;
                                /**
                                 * @description New JWT refresh token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                refreshToken: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/reset-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset Password
         * @description Reset password using token received via email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description Reset password data */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Reset password token received via email
                         * @example abc123def456
                         */
                        token: string;
                        /**
                         * @description New password (minimum 6 characters)
                         * @example newPassword123
                         */
                        newPassword: string;
                        /**
                         * @description Confirm new password
                         * @example newPassword123
                         */
                        newPasswordConfirmation: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * SignIn
         * @description Login with credential (Email/Mobile Number/NIK)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description User credential, fill with Email/Mobile Number/NIK
                         * @example <EMAIL> | 08123456789 | 123456789123456
                         */
                        credential: string;
                        password: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description JWT access token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                accessToken?: string;
                                /**
                                 * @description JWT refresh token
                                 * @example eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                                 */
                                refreshToken?: string;
                                /** @description User information */
                                user: {
                                    /**
                                     * @description User ID
                                     * @example Qah4cccEK0MsspUJ9ywAy
                                     */
                                    id: string;
                                    /**
                                     * @description User full name
                                     * @example John Doe
                                     */
                                    name: string;
                                    /**
                                     * Format: email
                                     * @description User email address
                                     * @example <EMAIL>
                                     */
                                    email: string;
                                    /**
                                     * @description User profile image URL
                                     * @example https://example.com/avatar.jpg
                                     */
                                    image: string;
                                    /**
                                     * @description Array of user role
                                     * @example [
                                     *       "Director"
                                     *     ]
                                     */
                                    roles: string[];
                                };
                                /**
                                 * @description Whether user has registered face samples for biometric authentication
                                 * @example true
                                 */
                                isFaceSampleRegistered: boolean;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/incentives/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Incentive Status
         * @description Get incentive eligibility status for all three incentive types (Attendance, Presence, Motor) for the authenticated user
         */
        get: {
            parameters: {
                query?: {
                    month?: number;
                    year?: number;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /** @description List of incentive status for all three types */
                                incentives: {
                                    /**
                                     * @description Type of incentive (ATTENDANCE, PRESENCE, MOTOR)
                                     * @example ATTENDANCE
                                     */
                                    incentiveType: string;
                                    /**
                                     * @description Whether user is eligible for this incentive
                                     * @example true
                                     */
                                    isEligible: boolean;
                                    /**
                                     * @description Period for incentive calculation based on cut-off rules
                                     * @example 2024-12-26_2025-01-25
                                     */
                                    period: string;
                                    /**
                                     * @description Additional notes about incentive eligibility
                                     * @example Memenuhi semua syarat insentif absensi
                                     */
                                    notes: string | null;
                                    requirements: {
                                        noLateCheckIn: boolean;
                                        noEarlyCheckOut: boolean;
                                        noMissedAttendance: boolean;
                                        noLeaveNotCountedAsPresent: boolean;
                                        fullMonthPresence: boolean;
                                        allOfficeLeaveOfficial: boolean;
                                    };
                                }[];
                            };
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/kpi/points": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get KPI Points
         * @description Get KPI points calculation for the authenticated user including task scores, attendance percentage, violation penalties, and final KPI score
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Period of KPI calculation (YYYY-MM for monthly, YYYY for yearly)
                                 * @example 2025-01
                                 */
                                period: string;
                                /**
                                 * @description Type of period (MONTHLY, YEARLY)
                                 * @example MONTHLY
                                 */
                                periodType: string;
                                /**
                                 * @description Task average point
                                 * @example 79
                                 */
                                taskPoint: number;
                                /**
                                 * @description Violation penalty point
                                 * @example 8
                                 */
                                violationPenaltyPoint: number;
                                /**
                                 * @description Final KPI point
                                 * @example 78
                                 */
                                finalPoint: number;
                                /** @example true */
                                canAddViolation: boolean;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/leave-requests/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action Leave Request [Admin]
         * @description Bulk action leave request for admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/leave-policies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Leave Policy [Admin]
         * @description Create a new leave policy (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description Leave policy data */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Leave policy name
                         * @example Annual Leave
                         */
                        name: string;
                        /**
                         * @description Optional description for the leave policy
                         * @example Annual leave policy for all employees
                         */
                        description?: string;
                        /**
                         * @description quota of leave policy in number of days
                         * @example 12
                         */
                        quota: number | null;
                        /**
                         * @description Active year for this policy
                         * @example 2025
                         */
                        effectiveYear: number;
                        /**
                         * @description Whether this leave policy is counted as present
                         * @default false
                         * @example true
                         */
                        isCountedAsPresent?: boolean;
                    };
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeavePolicy"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/leave-requests": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Leave Request [Admin]
         * @description Get all leave request for tabular data, support sorting, filtering, paginating, and searching
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"][];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create Leave Request [Admin]
         * @description Create new leave request for admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description ID of the user requesting the leave
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        userId: string;
                        /**
                         * @description ID of the leave policy to apply for
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        leavePolicyId: string;
                        /**
                         * Format: date
                         * @description Start date of leave in YYYY-MM-DD format
                         * @example 2025-01-15
                         */
                        startDate: string;
                        /**
                         * Format: date
                         * @description End date of leave in YYYY-MM-DD format
                         * @example 2025-01-17
                         */
                        endDate: string;
                        /**
                         * @description Reason or description for the leave request
                         * @example Family vacation
                         */
                        description: string;
                        /**
                         * Format: binary
                         * @description Optional supporting document (PDF, DOC, DOCX, max 5MB)
                         */
                        document?: string;
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/leave-requests/{leaveRequestId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Leave Request [Admin]
         * @description Get single leave request for admin
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    leaveRequestId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequest"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update Leave Request [Admin]
         * @description Create new leave request for admin
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    leaveRequestId: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description ID of the user requesting the leave
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        userId?: string;
                        /**
                         * @description ID of the leave policy to apply for
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        leavePolicyId?: string;
                        /**
                         * Format: date
                         * @description Start date of leave in YYYY-MM-DD format
                         * @example 2025-01-15
                         */
                        startDate?: string;
                        /**
                         * Format: date
                         * @description End date of leave in YYYY-MM-DD format
                         * @example 2025-01-17
                         */
                        endDate?: string;
                        /**
                         * @description Reason or description for the leave request
                         * @example Family vacation
                         */
                        description?: string;
                        /**
                         * Format: binary
                         * @description Optional supporting document (PDF, DOC, DOCX, max 5MB)
                         */
                        document?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/leave-requests": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Leave Request
         * @description Create a new leave request with optional document upload
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description ID of the leave policy to apply for
                         * @example Qah4cccEK0MsspUJ9ywAy
                         */
                        leavePolicyId: string;
                        /**
                         * Format: date
                         * @description Start date of leave in YYYY-MM-DD format
                         * @example 2025-01-15
                         */
                        startDate: string;
                        /**
                         * Format: date
                         * @description End date of leave in YYYY-MM-DD format
                         * @example 2025-01-17
                         */
                        endDate: string;
                        /**
                         * @description Reason or description for the leave request
                         * @example Family vacation
                         */
                        description: string;
                        /**
                         * Format: binary
                         * @description Optional supporting document (PDF, DOC, DOCX, max 5MB)
                         */
                        document?: string;
                    };
                };
            };
            responses: {
                /** @description Leave request created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/leave-policies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Active Leave Policy
         * @description Get list of active leave policies
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Leave policies retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeavePolicy"];
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/leave-requests/latest": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Latest Leave Request
         * @description Get current user's latest leave requests
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Leave requests retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/leave-requests/quota": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Leave Request Quota
         * @description Get current user's leave quota for all leave types
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Leave quota retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description unique identity
                                 * @example zNKqWgGbr2bqhocdueXSk
                                 */
                                id: string;
                                /**
                                 * @description Total remaining for quota
                                 * @example 15
                                 */
                                remaining: number;
                                /**
                                 * @description Name of leave quota
                                 * @example Cuti Menikah
                                 */
                                name: string;
                                /**
                                 * @description Total Quota
                                 * @example 20
                                 */
                                quota: number;
                                /**
                                 * @description Used
                                 * @example 5
                                 */
                                used: number;
                                /** @example true */
                                isActive: boolean;
                            }[];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/leave-requests/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Leave Request History
         * @description Get current user's leave request history
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Leave request history retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["LeaveRequestResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/office-leaves/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action Office Leave [Admin]
         * @description Bulk Action office leaves for admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/office-leaves": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Office Leave [Admin]
         * @description Get all office leave for admin
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeaveResponseDTO"][];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create Office Leave [Admin]
         * @description Create officel leave for admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description ID of the user requesting office leave
                         * @example user_bXn1LnLm8GNqn2OKSE4db
                         */
                        userId: string;
                        /**
                         * @description Title/name of the office leave request
                         * @example Client Meeting at Downtown Office
                         */
                        title: string;
                        /**
                         * @description Description/reason for office leave
                         * @example Meeting with ABC Corp client to discuss Q1 project requirements
                         */
                        description: string;
                        /**
                         * @description Start time of office leave in ISO format
                         * @example 09:00:00
                         */
                        startTime: string;
                        /**
                         * @description End time of office leave in ISO format
                         * @example 17:00:00
                         */
                        endTime: string;
                        /**
                         * Format: date
                         * @description Date of office leave (Date format)
                         * @example 2025-01-20
                         */
                        date: string;
                    };
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeaveResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/office-leaves/{officeLeaveId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Office Leave [Admin]
         * @description Get single office leave for admin
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    officeLeaveId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeave"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update Office Leave [Admin]
         * @description UPdate officel leave for admin
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    officeLeaveId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description ID of the user requesting office leave
                         * @example user_bXn1LnLm8GNqn2OKSE4db
                         */
                        userId?: string;
                        /**
                         * @description Title/name of the office leave request
                         * @example Client Meeting at Downtown Office
                         */
                        title?: string;
                        /**
                         * @description Description/reason for office leave
                         * @example Meeting with ABC Corp client to discuss Q1 project requirements
                         */
                        description?: string;
                        /**
                         * @description Start time of office leave in ISO format
                         * @example 09:00:00
                         */
                        startTime?: string;
                        /**
                         * @description End time of office leave in ISO format
                         * @example 17:00:00
                         */
                        endTime?: string;
                        /**
                         * Format: date
                         * @description Date of office leave (Date format)
                         * @example 2025-01-20
                         */
                        date?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeaveResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/office-leaves": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Office Leave
         * @description Create a new office leave request
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Title/name of the office leave request
                         * @example Client Meeting at Downtown Office
                         */
                        title: string;
                        /**
                         * @description Description/reason for office leave
                         * @example Meeting with ABC Corp client to discuss Q1 project requirements
                         */
                        description: string;
                        /**
                         * @description Start time of office leave in ISO format
                         * @example 09:00:00
                         */
                        startTime: string;
                        /**
                         * @description End time of office leave in ISO format
                         * @example 17:00:00
                         */
                        endTime: string;
                    };
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeaveResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/office-leaves/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Office Leave History
         * @description Get current user's office leave history
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                officeLeaves: components["schemas"]["OfficeLeaveResponseDTO"][];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/office-leaves/{officeLeaveId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Office Leave Detail
         * @description Get detailed information about a specific office leave
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Office Leave Id */
                    officeLeaveId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["OfficeLeaveResponseDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/office-leaves/for-review": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Office Leave For Review (HR)
         * @description Get office leaves that need review/categorization
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                officeLeaves: components["schemas"]["OfficeLeaveResponseDTO"][];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/office-leaves/{officeLeaveId}/mark": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Mark Office Leave
         * @description Mark office leave as official business or personal (Admin only)
         */
        patch: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Office Leave Id */
                    officeLeaveId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Whether this office leave is for official business
                         * @example true
                         */
                        isOfficialBusiness: boolean;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        trace?: never;
    };
    "/api/v1/admin/roles/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action Role [Admin]
         * @description Bulk Action (Export, Update) for role entities (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Role [Admin]
         * @description Get all roles for tabular data, support sorting, filtering, paginating, and searching
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: (components["schemas"]["Role"] & {
                                parentName: string | null;
                            })[];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New Role [Admin]
         * @description Create a new role (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        name: string;
                        description?: string;
                        parentId?: string;
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Role"] & {
                                parentName: string | null;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/roles/options": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Role Options [Admin]
         * @description Get role options for dropdown/select data (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                label: string;
                                value: string | number;
                            }[];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/roles/tree": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Role Tree [Admin]
         * @description Get role tree nodes (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetTreeNodeResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/roles/{roleId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Role [Admin]
         * @description Get role single role (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    roleId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Role"] & {
                                parentName: string | null;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update Role [Admin]
         * @description Update role (Admin only)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    roleId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        name?: string;
                        description?: string;
                        parentId?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Role"] & {
                                parentName: string | null;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/roles/{roleId}/supervisors/options": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Supervisor For Specific Role [Admin]
         * @description Get list of supervisor users for a specific role (Admin only)
         */
        get: {
            parameters: {
                query?: {
                    excludeId?: string;
                };
                header?: never;
                path: {
                    /** @description Role Id */
                    roleId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                label: string;
                                value: string | number;
                            }[];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{taskId}/accept-reject": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Accept/Reject Task
         * @description Accept or reject task (for assignees)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Task ID */
                    taskId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Action to perform (ACCEPT or REJECT)
                         * @example ACCEPT
                         * @enum {string}
                         */
                        action: "ACCEPT" | "REJECT";
                        /**
                         * @description Reason for rejection (required if action is REJECT)
                         * @example Does not meet quality standards
                         */
                        rejectionReason?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/home": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Data For Home
         * @description Get task data for home task page
         */
        get: {
            parameters: {
                query?: {
                    /** @description User id for viewing subordinate task data */
                    viewerId?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /** @example false */
                                isViewingOtherUser: boolean;
                                /**
                                 * @description Will null if isViewingOtherUser is false
                                 * @example John Cena
                                 */
                                viewedUserName: string | null;
                                /**
                                 * @description State for show/hide the select component for viewing other user
                                 * @example true
                                 */
                                showSelectSubordinateOption: boolean;
                                /**
                                 * @description State for show/hide add task button
                                 * @example true
                                 */
                                showAddTaskButton: boolean;
                                /**
                                 * @description State for show/hide subordinate task list for review section
                                 * @example true
                                 */
                                showSubordinateTaskListSection: boolean;
                                subordinateList: {
                                    /**
                                     * @description Unique identity of user
                                     * @example iOBn-Zez5pwumx_jgVDv3
                                     */
                                    id: string;
                                    /**
                                     * @description user name
                                     * @example John doe
                                     */
                                    name: string;
                                    /**
                                     * @description user email
                                     * @example <EMAIL>
                                     */
                                    email: string;
                                    /**
                                     * @description Indicate this user is current logged-in user
                                     * @example true
                                     */
                                    isCurrentUser: boolean;
                                    /**
                                     * @description Indicate this current viewing for user
                                     * @example true
                                     */
                                    isCurrentView: boolean;
                                }[] | null;
                                subordinateTasks: (components["schemas"]["Task"] & {
                                    /** @example Hoon */
                                    assignerName: string;
                                    /** @example John doe */
                                    assigneeName: string;
                                })[] | null;
                                taskStats: {
                                    /**
                                     * @description Total task with pending status
                                     * @example 0
                                     */
                                    PENDING: number;
                                    /**
                                     * @description Total task with in_progress status
                                     * @example 0
                                     */
                                    IN_PROGRESS: number;
                                    /**
                                     * @description Total task with in_review status
                                     * @example 0
                                     */
                                    IN_REVIEW: number;
                                    /**
                                     * @description Total task with revision_required status
                                     * @example 0
                                     */
                                    REVISION_REQUIRED: number;
                                    /**
                                     * @description Total task with completed status
                                     * @example 0
                                     */
                                    COMPLETED: number;
                                    /**
                                     * @description Total task with rejected status
                                     * @example 0
                                     */
                                    REJECTED: number;
                                };
                                myTaskTabs: {
                                    /** @example HBaLfiQwz1 */
                                    tabId: string;
                                    /** @example Selesai */
                                    tabTitle: string;
                                    /** @example /api/v1/tasks?status=COMPLETED */
                                    tabSource: string;
                                    /** @example #000000 */
                                    color: string;
                                    /** @example 5 */
                                    count: number;
                                    /** @example false */
                                    isDefault: boolean;
                                    filters: {
                                        /** @example COMPLETED */
                                        status: string;
                                    };
                                }[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Task With Filter
         * @description Get all task list with filter, default is viewing PENDING status and self tasks
         */
        get: {
            parameters: {
                query?: {
                    /** @description Task status */
                    status?: "COMPLETED" | "IN_PROGRESS" | "IN_REVIEW" | "PENDING" | "REJECTED" | "REVISION_REQUIRED";
                    /** @description Viewing for the userId data */
                    userId?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                tasks: (components["schemas"]["Task"] & {
                                    /** @example Hoon */
                                    assignerName: string;
                                    /** @example John doe */
                                    assigneeName: string;
                                    /** @example #D6D6D6 */
                                    color: string;
                                    /**
                                     * @example Perlu Persetujuan Anda
                                     * @enum {string}
                                     */
                                    displayStatusLabel: "Tugas Baru" | "Dikerjakan" | "Dalam Pengecekan" | "Perlu Revisi" | "Selesai" | "Ditolak";
                                })[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New Task
         * @description Create new task (creates independent task for each assignee)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description Task name/title
                         * @example Monthly Sales Report
                         */
                        name: string;
                        /**
                         * @description Task description/details
                         * @example Prepare and submit the monthly sales report for Q1
                         */
                        description: string;
                        /**
                         * Format: date
                         * @description Task deadline in YYYY-MM-DD format
                         * @example 2025-02-15
                         */
                        deadline: string;
                        /**
                         * @description Array of user IDs to assign the task to
                         * @example [
                         *       "user1",
                         *       "user2"
                         *     ]
                         */
                        assigneeIds: string[];
                        /** @description Task document files (All files, max 10MB each, max 5 files) */
                        documents?: string[];
                        /** @description Task image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files) */
                        images?: string[];
                    };
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                tasks: (components["schemas"]["Task"] & {
                                    /** @example Hoon */
                                    assignerName: string;
                                    /** @example John doe */
                                    assigneeName: string;
                                    /** @example #D6D6D6 */
                                    color: string;
                                    /**
                                     * @example Perlu Persetujuan Anda
                                     * @enum {string}
                                     */
                                    displayStatusLabel: "Tugas Baru" | "Dikerjakan" | "Dalam Pengecekan" | "Perlu Revisi" | "Selesai" | "Ditolak";
                                })[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/assignable-users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Assignable User for Task
         * @description Get users that can be assigned tasks based on hierarchy
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                users: {
                                    /**
                                     * @description Unique identity of user
                                     * @example _1RPSu7WmsO5ckxDh2YK6
                                     */
                                    id: string;
                                    /**
                                     * @description User name
                                     * @example John
                                     */
                                    name: string;
                                    /**
                                     * @description User email
                                     * @example <EMAIL>
                                     */
                                    email: string;
                                    /**
                                     * @description Active task for the user
                                     * @example 4
                                     */
                                    activeTask: number;
                                }[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{taskId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Detail
         * @description Get task detail by id
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Task ID */
                    taskId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Task"] & {
                                /** @example Hoon */
                                assignerName: string;
                                /** @example John doe */
                                assigneeName: string;
                                /** @example #D6D6D6 */
                                color: string;
                                /**
                                 * @example Perlu Persetujuan Anda
                                 * @enum {string}
                                 */
                                displayStatusLabel: "Tugas Baru" | "Dikerjakan" | "Dalam Pengecekan" | "Perlu Revisi" | "Selesai" | "Ditolak";
                                submissions: {
                                    /**
                                     * @description Unique identifier for this specific submission record
                                     * @example hU7dWKoEqJ2RwaOG121FC
                                     */
                                    id?: string | null;
                                    /**
                                     * @description ID of the task this submission belongs to
                                     * @example V07U3oADuyaTmyZ3Mmm9M
                                     */
                                    taskId: string;
                                    /**
                                     * @description ID of the user who submitted this submission
                                     * @example 5aSgbw0NJTWL1OqarrBn-
                                     */
                                    userId: string;
                                    /**
                                     * @description ID of the user who reviewed this submission
                                     * @example imnDsBjerxHSocXT6M5tU
                                     */
                                    reviewedBy: string | null;
                                    /**
                                     * @description Sequential number of this submission attempt (e.g., 1st, 2nd, 3rd submission)
                                     * @example 1
                                     */
                                    submissionNumber: number;
                                    /**
                                     * @description URLs of documents/files submitted for THIS specific submission attempt
                                     * @example ['/uploads/tasks/submission-v1-doc.pdf']
                                     */
                                    documentUrls: string | null;
                                    /**
                                     * @description Status of this specific submission attempt (PENDING_REVIEW, ACCEPTED, REJECTED)
                                     * @example PENDING_REVIEW
                                     * @enum {string}
                                     */
                                    status: "ACCEPTED" | "REJECTED" | "REJECTED";
                                    /**
                                     * Format: date-time
                                     * @description Timestamp when this submission was made
                                     * @example 2025-01-15T10:30:00Z
                                     */
                                    submittedAt: string;
                                    /**
                                     * Format: date-time
                                     * @description Timestamp when this submission was reviewed
                                     * @example 2025-01-16T14:00:00Z
                                     */
                                    reviewDate: string | null;
                                    /**
                                     * @description General feedback from the supervisor for this submission attempt.
                                     * @example Overall good effort, but need to fix formatting on page 5.
                                     */
                                    feedback: string | null;
                                    /**
                                     * @description Notes provided by the assignee for the supervisor
                                     * @example There is some notes for the supervisor
                                     */
                                    notes: string | null;
                                }[];
                                /**
                                 * @description Possible value: ["acceptTask", "rejectTask"] | ["submitTask"] | ["markAsComplete", "markNeedRevision"]
                                 * @example [
                                 *       "acceptTask, rejectTask"
                                 *     ]
                                 */
                                uiActions: string[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/points": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Task That Have A Point
         * @description Get all task list that have a point
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                tasks: (components["schemas"]["Task"] & {
                                    /** @example Hoon */
                                    assignerName: string;
                                    /** @example John doe */
                                    assigneeName: string;
                                    /** @example #D6D6D6 */
                                    color: string;
                                    /**
                                     * @example Perlu Persetujuan Anda
                                     * @enum {string}
                                     */
                                    displayStatusLabel: "Tugas Baru" | "Dikerjakan" | "Dalam Pengecekan" | "Perlu Revisi" | "Selesai" | "Ditolak";
                                })[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{taskId}/review": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Mark Task Completed/Revision
         * @description Mark task as completed or revision required (for assigners/reviewers)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Task ID */
                    taskId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Action to perform (COMPLETED or REVISION_REQUIRED)
                         * @example COMPLETED
                         * @enum {string}
                         */
                        action: "COMPLETED" | "REVISION_REQUIRED";
                        /**
                         * @description Task rating score (0-100)
                         * @example 85
                         */
                        ratingPoint?: number;
                        /**
                         * @description General feedback from the supervisor for the task/submission, if action is completed, will be saved as finalFeedback
                         * @example Overall good effort, but need to fix formatting on page 5.
                         */
                        feedback?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{taskId}/reassign": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Re-Assign Task
         * @description Re-assign task to another user (for assigners)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Task ID */
                    taskId: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description ID of the user to reassign the task to
                         * @example user123
                         */
                        assigneeId: string;
                        /**
                         * @description Updated task name (optional)
                         * @example Updated Monthly Sales Report
                         */
                        name?: string;
                        /**
                         * @description Updated task description (optional)
                         * @example Updated description for the task
                         */
                        description?: string;
                        /**
                         * Format: date
                         * @description Updated task deadline (optional)
                         * @example 2025-03-15
                         */
                        deadline?: string;
                        /** @description Task document files (All files, max 10MB each, max 5 files) */
                        documents?: string[];
                        /** @description Task image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files) */
                        images?: string[];
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Task"] & {
                                /** @example Hoon */
                                assignerName: string;
                                /** @example John doe */
                                assigneeName: string;
                                /** @example #D6D6D6 */
                                color: string;
                                /**
                                 * @example Perlu Persetujuan Anda
                                 * @enum {string}
                                 */
                                displayStatusLabel: "Tugas Baru" | "Dikerjakan" | "Dalam Pengecekan" | "Perlu Revisi" | "Selesai" | "Ditolak";
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{taskId}/submit": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit Task Submission
         * @description Submit task with documents and notes (for assignees)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Task ID */
                    taskId: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "multipart/form-data": {
                        /**
                         * @description Submission notes (optional)
                         * @example Task completed successfully. Please review the attached documents.
                         */
                        notes?: string;
                        /** @description Task submission document files (All files, max 10MB each, max 5 files) */
                        documents?: string[];
                        /** @description Task submission image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files) */
                        images?: string[];
                    };
                };
            };
            responses: {
                /** @description Task submitted successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            /** @description Optional data object returned by the API */
                            data?: unknown;
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Not Found. Resource not found. */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/administrator-users/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action Administrator User [Admin]
         * @description Bulk Action for administrator user entities (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users/bulk-action": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Bulk Action User [Admin]
         * @description Bulk Action (Export, Update) for user entities (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        action: "delete" | "export" | "update";
                        selection: {
                            isSelectAll: boolean;
                            selectedIds?: string[];
                            excludedIds?: string[];
                        };
                        queryString?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data?: unknown;
                        };
                        "text/csv": string;
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/administrator-users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Administrator User [Admin]
         * @description Get list of administrator user with paginating, filtering, sorting, and searching
         */
        get: {
            parameters: {
                query?: {
                    pageIndex?: number | null;
                    pageSize?: number;
                    search?: string;
                    sort?: string;
                    name?: string | (string)[];
                    email?: string | (string)[];
                    createdAt?: string | (string)[];
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Unique identifier for the admin user
                                 * @example 8xYW-nl9KZ_bgf0QiiUtL
                                 */
                                id: string;
                                /**
                                 * @description Name of the admin user
                                 * @example IT Admin
                                 */
                                name: string;
                                /**
                                 * @description Username of administrator user
                                 * @example <EMAIL>
                                 */
                                email: string;
                                /**
                                 * @description User profile image URL
                                 * @example /uploads/avatars/user-avatar-123.jpg
                                 */
                                image: string;
                                /**
                                 * @description Whether email is verified
                                 * @example true
                                 */
                                emailVerified: boolean;
                                /**
                                 * @description Id of the primary account (to link the admin account to an actual individual).
                                 * @example JwYEtjGl2A9xWaqRxjTXf
                                 */
                                userId: string;
                                /**
                                 * @description Id of the administrator role
                                 * @example R0f9JhkY--RZc_31bOp_Q
                                 */
                                roleId: string;
                                /**
                                 * Format: date-time
                                 * @description Creation timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                createdAt: string;
                                /**
                                 * Format: date-time
                                 * @description Last update timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                updatedAt: string;
                                userName: string;
                                userEmail: string;
                            }[];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New Administrator User [Admin]
         * @description Create a new administrator user account (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Id user related to this account
                         * @example HpIYxfeqOdO0lVqr8S9dZ
                         */
                        userId: string;
                        /**
                         * @description Full name of the user
                         * @example John Doe
                         */
                        name: string;
                        /**
                         * Format: email
                         * @description Email address of the user
                         * @example <EMAIL>
                         */
                        email: string;
                        /**
                         * @description Optional password field
                         * @example securePassword123
                         */
                        password?: string;
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Unique identifier for the admin user
                                 * @example 8xYW-nl9KZ_bgf0QiiUtL
                                 */
                                id: string;
                                /**
                                 * @description Name of the admin user
                                 * @example IT Admin
                                 */
                                name: string;
                                /**
                                 * @description Username of administrator user
                                 * @example <EMAIL>
                                 */
                                email: string;
                                /**
                                 * @description User profile image URL
                                 * @example /uploads/avatars/user-avatar-123.jpg
                                 */
                                image: string;
                                /**
                                 * @description Whether email is verified
                                 * @example true
                                 */
                                emailVerified: boolean;
                                /**
                                 * @description Id of the primary account (to link the admin account to an actual individual).
                                 * @example JwYEtjGl2A9xWaqRxjTXf
                                 */
                                userId: string;
                                /**
                                 * @description Id of the administrator role
                                 * @example R0f9JhkY--RZc_31bOp_Q
                                 */
                                roleId: string;
                                /**
                                 * Format: date-time
                                 * @description Creation timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                createdAt: string;
                                /**
                                 * Format: date-time
                                 * @description Last update timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                updatedAt: string;
                                userName: string;
                                userEmail: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All User [Admin]
         * @description Get list of user with paginating, filtering, sorting, and searching
         */
        get: {
            parameters: {
                query?: {
                    pageIndex?: number | null;
                    pageSize?: number;
                    search?: string;
                    sort?: string;
                    name?: string | (string)[];
                    email?: string | (string)[];
                    roleName?: string | (string)[];
                    supervisorName?: string | (string)[];
                    createdAt?: string | (string)[];
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: (components["schemas"]["User"] & {
                                roleId: string;
                                roleName: string;
                                supervisorId: string | null;
                                supervisorName: string | null;
                                supervisorEmail: string | null;
                            })[];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New User [Admin]
         * @description Create a new user account (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Role ID assigned to user
                         * @example dJYbsuY0iO52io5rQoBrE
                         */
                        roleId: string;
                        /**
                         * @description Supervisor ID if roles has list of supervisors
                         * @example 14piZkkeYzqskZvP_skSJ
                         */
                        supervisorId: string | null;
                        /**
                         * @description Full name of the user
                         * @example John Doe
                         */
                        name: string;
                        /**
                         * Format: email
                         * @description Email address of the user
                         * @example <EMAIL>
                         */
                        email: string;
                        /**
                         * @description National Identity Number (NIK)
                         * @example 1234567890123456
                         */
                        nik: string;
                        /**
                         * @description Mobile phone number
                         * @example 08123456789
                         */
                        mobileNumber: string;
                        /**
                         * @description Optional password field
                         * @example securePassword123
                         */
                        password?: string;
                        worksites?: {
                            /**
                             * @description Work site ID
                             * @example Qah4cccEK0MsspUJ9ywAy
                             */
                            id: string;
                            /**
                             * @description Whether this is the main work site for the user
                             * @example true
                             */
                            isMain?: boolean | null;
                        }[];
                    };
                };
            };
            responses: {
                /** @description created */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["User"] & {
                                roleId: string;
                                roleName: string;
                                supervisorId: string | null;
                                supervisorName: string | null;
                                supervisorEmail: string | null;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/administrator-users/{administratorUserId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Administrator User [Admin]
         * @description Get single administrator user
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    administratorUserId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Unique identifier for the admin user
                                 * @example 8xYW-nl9KZ_bgf0QiiUtL
                                 */
                                id: string;
                                /**
                                 * @description Name of the admin user
                                 * @example IT Admin
                                 */
                                name: string;
                                /**
                                 * @description Username of administrator user
                                 * @example <EMAIL>
                                 */
                                email: string;
                                /**
                                 * @description User profile image URL
                                 * @example /uploads/avatars/user-avatar-123.jpg
                                 */
                                image: string;
                                /**
                                 * @description Whether email is verified
                                 * @example true
                                 */
                                emailVerified: boolean;
                                /**
                                 * @description Id of the primary account (to link the admin account to an actual individual).
                                 * @example JwYEtjGl2A9xWaqRxjTXf
                                 */
                                userId: string;
                                /**
                                 * @description Id of the administrator role
                                 * @example R0f9JhkY--RZc_31bOp_Q
                                 */
                                roleId: string;
                                /**
                                 * Format: date-time
                                 * @description Creation timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                createdAt: string;
                                /**
                                 * Format: date-time
                                 * @description Last update timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                updatedAt: string;
                                userName: string;
                                userEmail: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update Administrator User [Admin]
         * @description Update administrator user (Admin only)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    administratorUserId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Id user related to this account
                         * @example HpIYxfeqOdO0lVqr8S9dZ
                         */
                        userId?: string;
                        /**
                         * @description Full name of the user
                         * @example John Doe
                         */
                        name?: string;
                        /**
                         * Format: email
                         * @description Email address of the user
                         * @example <EMAIL>
                         */
                        email?: string;
                        /**
                         * @description Optional password field
                         * @example securePassword123
                         */
                        password?: string;
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                /**
                                 * @description Unique identifier for the admin user
                                 * @example 8xYW-nl9KZ_bgf0QiiUtL
                                 */
                                id: string;
                                /**
                                 * @description Name of the admin user
                                 * @example IT Admin
                                 */
                                name: string;
                                /**
                                 * @description Username of administrator user
                                 * @example <EMAIL>
                                 */
                                email: string;
                                /**
                                 * @description User profile image URL
                                 * @example /uploads/avatars/user-avatar-123.jpg
                                 */
                                image: string;
                                /**
                                 * @description Whether email is verified
                                 * @example true
                                 */
                                emailVerified: boolean;
                                /**
                                 * @description Id of the primary account (to link the admin account to an actual individual).
                                 * @example JwYEtjGl2A9xWaqRxjTXf
                                 */
                                userId: string;
                                /**
                                 * @description Id of the administrator role
                                 * @example R0f9JhkY--RZc_31bOp_Q
                                 */
                                roleId: string;
                                /**
                                 * Format: date-time
                                 * @description Creation timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                createdAt: string;
                                /**
                                 * Format: date-time
                                 * @description Last update timestamp
                                 * @example 2025-01-15T08:30:15.000Z
                                 */
                                updatedAt: string;
                                userName: string;
                                userEmail: string;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users/options": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Options [Admin]
         * @description Get user options for selectable label - option
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: {
                                label: string;
                                value: string | number;
                            }[];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users/tree": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Tree [Admin]
         * @description Get user tree nodes
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetTreeNodeResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users/{userId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User [Admin]
         * @description Get single user
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    userId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["User"] & {
                                roleId: string;
                                roleName: string;
                                supervisorId: string | null;
                                supervisorName: string | null;
                                supervisorEmail: string | null;
                                worksites: {
                                    id: string;
                                    isMain: boolean;
                                }[];
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        /**
         * Update User [Admin]
         * @description Update user (Admin only)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    userId: string;
                };
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Role ID assigned to user
                         * @example dJYbsuY0iO52io5rQoBrE
                         */
                        roleId?: string;
                        /**
                         * @description Supervisor ID if roles has list of supervisors
                         * @example 14piZkkeYzqskZvP_skSJ
                         */
                        supervisorId?: string | null;
                        /**
                         * @description Full name of the user
                         * @example John Doe
                         */
                        name?: string;
                        /**
                         * Format: email
                         * @description Email address of the user
                         * @example <EMAIL>
                         */
                        email?: string;
                        /**
                         * @description National Identity Number (NIK)
                         * @example 1234567890123456
                         */
                        nik?: string;
                        /**
                         * @description Mobile phone number
                         * @example 08123456789
                         */
                        mobileNumber?: string;
                        /**
                         * @description Optional password field
                         * @example securePassword123
                         */
                        password?: string;
                        worksites?: {
                            /**
                             * @description Work site ID
                             * @example Qah4cccEK0MsspUJ9ywAy
                             */
                            id: string;
                            /**
                             * @description Whether this is the main work site for the user
                             * @example true
                             */
                            isMain?: boolean | null;
                        }[];
                    };
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["User"] & {
                                roleId: string;
                                roleName: string;
                                supervisorId: string | null;
                                supervisorName: string | null;
                                supervisorEmail: string | null;
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/violation-types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create New Violation Type [Admin]
         * @description Create new violation types/category for admin
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": {
                        /**
                         * @description Violation type name
                         * @example Terlambat Masuk Kerja
                         */
                        name: string;
                        /**
                         * @description Violation type description
                         * @example Pelanggaran ketika karyawan terlambat masuk kerja
                         */
                        description?: string;
                        /**
                         * @description Penalty points for this violation type
                         * @example 10
                         */
                        penaltyPoints: number;
                        /**
                         * @description Punishment
                         * @example SP3
                         */
                        punishment: string;
                        /**
                         * @description Whether this violation type is active
                         * @default true
                         * @example true
                         */
                        isActive?: boolean;
                    };
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["ViolationTypeDTO"];
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/violations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create New Violation
         * @description Create new violation
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["CreateViolationDTO"];
                };
            };
            responses: {
                /** @description success */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["CreateViolationResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/violations/points": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Violation With Penalty Point
         * @description Get violation list for penalty points
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetMyViolationListPointResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/violations/types/{violationTypeId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Violation Type Detail
         * @description Get violation-type detail
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    /** @description Violation type Id */
                    violationTypeId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetViolationTypeDetailResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/violations/types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Violation Types
         * @description Get violation types/category
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetViolationTypeResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/worksites/assign": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Assign User to Worksite [Admin]
         * @description Assign a user to multiple work sites
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["AssignUserToWorkSitesDTO"];
                };
            };
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["AssignUserToWorksitesResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/worksites": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Worksite [Admin]
         * @description Get list of work sites with pagination and filtering (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /**
                             * @description Result status of the API call
                             * @example success
                             * @enum {string}
                             */
                            status: "success";
                            /**
                             * @description Descriptive message for the API result
                             * @example Operation completed successfully.
                             */
                            message: string;
                            data: components["schemas"]["Worksite"][];
                            /** @description Metadata information */
                            meta: {
                                /** @description Pagination information */
                                pagination: {
                                    /**
                                     * @description Current page number
                                     * @example 1
                                     */
                                    pageIndex: number;
                                    /**
                                     * @description Number of items per page
                                     * @example 10
                                     */
                                    pageSize: number;
                                    /**
                                     * @description Total number of items available
                                     * @example 100
                                     */
                                    totalItems: number;
                                    /**
                                     * @description Total number of pages available
                                     * @example 10
                                     */
                                    totalPages: number;
                                    hasNextPage: boolean;
                                    hasPreviousPage: boolean;
                                };
                            };
                        };
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        /**
         * Create New Worksite [Admin]
         * @description Create a new work site (Admin only)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            /** @description payload */
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["CreateWorkSiteDTO"];
                };
            };
            responses: {
                /** @description Work site created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["CreateWorksiteResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description UnProcessable Entity. Some validation error. */
                422: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                            errors: {
                                [key: string]: string[];
                            };
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/worksites/{worksiteId}/attendance-rule": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attendance Rule By Worksite [Admin]
         * @description Get attendance rule detail by id
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    worksiteId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetAttendanceRuleByWorksiteResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/worksites/options": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Worksite Options [Admin]
         * @description Get worksite options for dropdown/select data (Admin only)
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description success */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetWorksiteOptionsResponseDTO"];
                    };
                };
                /** @description Bad Request. Missing or invalid parameters. */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Unauthorized. Authentication required. */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Forbidden. You do not have permission. */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Too Many Requests. Rate limit exceeded. */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
                /** @description Internal Server Error. */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            status: "failed";
                            message: string;
                        };
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        BaseResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            /** @description Optional data object returned by the API */
            data?: unknown;
        };
        BaseErrorResponseDTO: {
            /**
             * @description Result status indicating failure
             * @example failed
             * @enum {string}
             */
            status: "failed";
            /**
             * @description Error message
             * @example Validation failed
             */
            message: string;
            /** @description Detailed validation errors, if any */
            errors?: {
                [key: string]: unknown;
            };
        };
        BasePaginationResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            /** @description Array of paginated items returned by the API */
            data: unknown[];
            /** @description Metadata information */
            meta: {
                /** @description Pagination information */
                pagination: {
                    /**
                     * @description Current page number
                     * @example 1
                     */
                    pageIndex: number;
                    /**
                     * @description Number of items per page
                     * @example 10
                     */
                    pageSize: number;
                    /**
                     * @description Total number of items available
                     * @example 100
                     */
                    totalItems: number;
                    /**
                     * @description Total number of pages available
                     * @example 10
                     */
                    totalPages: number;
                    hasNextPage: boolean;
                    hasPreviousPage: boolean;
                };
            };
        };
        TimestampDTO: {
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        BulkActionDTO: {
            /** @enum {string} */
            action: "delete" | "export" | "update";
            selection: {
                isSelectAll: boolean;
                selectedIds?: string[];
                excludedIds?: string[];
            };
            queryString?: string;
        };
        AdministratorUser: {
            /**
             * @description Unique identifier for the admin user
             * @example 8xYW-nl9KZ_bgf0QiiUtL
             */
            id: string;
            /**
             * @description Name of the admin user
             * @example IT Admin
             */
            name: string;
            /**
             * @description Username of administrator user
             * @example <EMAIL>
             */
            email: string;
            /**
             * @description Password
             * @example Password1
             */
            password: string;
            /**
             * @description User profile image URL
             * @example /uploads/avatars/user-avatar-123.jpg
             */
            image: string;
            /**
             * @description Whether email is verified
             * @example true
             */
            emailVerified: boolean;
            /**
             * @description Id of the primary account (to link the admin account to an actual individual).
             * @example JwYEtjGl2A9xWaqRxjTXf
             */
            userId: string;
            /**
             * @description Id of the administrator role
             * @example R0f9JhkY--RZc_31bOp_Q
             */
            roleId: string;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        Role: {
            /**
             * @description Unique identifier for the role
             * @example AoH_WyFs2SBqlmob081uI
             */
            id: string;
            /**
             * @description Name of the role
             * @example Director
             */
            name: string;
            /**
             * @description Description of the role
             * @example Role for directors
             */
            description: string | null;
            /**
             * @description ID of the parent role (null for top level)
             * @example 6jMyaEVAmlxeWHCzNfLPi
             */
            parentId: string | null;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        AttendanceLog: {
            /**
             * @description Unique identifier for the attendance log
             * @example att_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description ID of the user who recorded attendance
             * @example user_bXn1LnLm8GNqn2OKSE4db
             */
            userId: string;
            /**
             * @description ID of the work site where attendance was recorded
             * @example ws_Qah4cccEK0MsspUJ9ywAy
             */
            worksiteId: string;
            /**
             * @description Type of attendance
             * @example CHECK_IN
             * @enum {string}
             */
            type: "CHECK_IN" | "CHECK_OUT" | "BREAK" | "RETURN";
            /**
             * Format: date
             * @description Date of attendance (DATEONLY format)
             * @example 2025-01-15
             */
            logDate: string;
            /**
             * @description Time of attendance in HH:mm:ss:ss format
             * @example 08:30:15
             */
            logTime: string;
            /**
             * @description Attendance status
             * @example ON_TIME
             * @enum {string}
             */
            status: "ON_TIME" | "LATE";
            /**
             * @description Latitude coordinate of attendance location
             * @example -6.2088
             */
            locationLat: number;
            /**
             * @description Longitude coordinate of attendance location
             * @example 106.8456
             */
            locationLong: number;
            /**
             * @description Face recognition distance score (0-1)
             * @example 0.95
             */
            distanceScore: number;
            /**
             * @description Path to attendance photo
             * @example /uploads/attendance/user123/att-photo-xyz789.jpg
             */
            photo: string;
            /**
             * @description Device information used for attendance
             * @example Android 12, Samsung Galaxy S21
             */
            deviceInfo: string;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        AttendanceRule: {
            /**
             * @description Unique identifier for the attendance rule
             * @example ar_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description ID of the work site this rule applies to
             * @example ws_Qah4cccEK0MsspUJ9ywAy
             */
            worksiteId: string;
            /**
             * @description Latitude coordinate of the work site
             * @example -6.2088
             */
            latitude: number;
            /**
             * @description Longitude coordinate of the work site
             * @example 106.8456
             */
            longitude: number;
            /**
             * @description Allowed radius in meters for attendance check-in
             * @example 100
             */
            radiusInMeter: number;
            /**
             * @description Timezone for the work site
             * @example Asia/Jakarta
             */
            timezone: string;
            /**
             * @description Start time for check-in in HH:mm:ss format
             * @example 06:00:00
             */
            checkInStartTime: string;
            /**
             * @description End time for check-in in HH:mm:ss format
             * @example 10:15:00
             */
            checkInEndTime: string;
            /**
             * @description Tolerance in minutes for check-in
             * @example 30
             */
            checkInToleranceMinutes: number;
            /**
             * @description Start time for check-out in HH:mm:ss format
             * @example 17:00:00
             */
            checkOutStartTime: string;
            /**
             * @description End time for check-out in HH:mm:ss format
             * @example 23:59:00
             */
            checkOutEndTime: string;
            /**
             * @description Tolerance in minutes for check-out
             * @example 30
             */
            checkOutToleranceMinutes: number;
            /**
             * @description Start time for break in HH:mm:ss format
             * @example 12:00:00
             */
            breakStartTime: string;
            /**
             * @description End time for break in HH:mm:ss format
             * @example 13:00:00
             */
            breakEndTime: string;
            /**
             * @description Tolerance in minutes for break
             * @example 30
             */
            breakToleranceMinutes: number;
            /**
             * @description Start time for return in HH:mm:ss format
             * @example 13:00:00
             */
            returnStartTime: string;
            /**
             * @description End time for return in HH:mm:ss format
             * @example 14:00:00
             */
            returnEndTime: string;
            /**
             * @description Tolerance in minutes for return
             * @example 30
             */
            returnToleranceMinutes: number;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        LeavePolicy: {
            /**
             * @description Unique identifier for the leave policy
             * @example lp_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description Name of the leave policy
             * @example Annual Leave
             */
            name: string;
            /**
             * @description Description of the leave policy
             * @example Annual leave policy for all employees with 12 days quota
             */
            description: string | null;
            /**
             * @description Year when this policy is effective
             * @example 2025
             */
            effectiveYear: number;
            /**
             * @description Number of leave days allocated per year
             * @example 12
             */
            quota: number;
            /**
             * @description Whether this leave policy is counted as present
             * @example true
             */
            isCountedAsPresent: boolean;
            /**
             * @description Whether this leave policy is active
             * @example true
             */
            isActive: boolean;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        LeaveRequest: {
            /**
             * @description Unique identifier for the leave request
             * @example lr_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description ID of the user requesting leave
             * @example user_bXn1LnLm8GNqn2OKSE4db
             */
            userId: string;
            /**
             * @description ID of the leave balance record
             * @example lb_Qah4cccEK0MsspUJ9ywAy
             */
            leaveBalanceId: string;
            /**
             * @description ID of the leave policy being applied
             * @example lp_rJGI0UrX29J2pJN5lj9vN
             */
            leavePolicyId: string;
            /**
             * Format: date-time
             * @description Start date of leave (DATEONLY format)
             * @example 2025-01-15
             */
            startDate: string;
            /**
             * Format: date-time
             * @description End date of leave (DATEONLY format)
             * @example 2025-01-17
             */
            endDate: string;
            /**
             * @description Reason for the leave request
             * @example Family vacation to Bali
             */
            reason: string;
            /**
             * @description Status of leave request (PENDING, APPROVED, REJECTED)
             * @example PENDING
             */
            status: string;
            /**
             * @description Number of effective leave days (excluding weekends/holidays)
             * @example 3
             */
            effectiveLeaveDays: number;
            /**
             * @description URL to supporting document
             * @example /uploads/leave-docs/leave-doc-123.pdf
             */
            documentUrl: string | null;
            /**
             * @description ID of HR who reviewed the request
             * @example hr_manager_456
             */
            reviewedBy: string | null;
            /**
             * Format: date-time
             * @description When the request was reviewed
             * @example 2025-01-14T14:30:00Z
             */
            reviewedAt: string | null;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        LeaveRequestResponseDTO: components["schemas"]["LeaveRequest"] & {
            name: string;
            isCountedAsPresent: boolean;
            userName: string;
            userEmail: string;
            reviewerName: string | null;
            reviewerEmail: string | null;
        };
        OfficeLeave: {
            /**
             * @description Unique identifier for the office leave
             * @example ol_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description ID of the user requesting office leave
             * @example user_bXn1LnLm8GNqn2OKSE4db
             */
            userId: string;
            /**
             * @description Title/name of the office leave request
             * @example Client Meeting at Downtown Office
             */
            title: string;
            /**
             * @description Description/reason for office leave
             * @example Meeting with ABC Corp client to discuss Q1 project requirements
             */
            description: string;
            /**
             * @example NEED_REVIEW
             * @enum {string}
             */
            status: "NEED_REVIEW" | "REVIEWED";
            /**
             * @description Start time of office leave (DateTime format)
             * @example 09:00:00
             */
            startTime: string;
            /**
             * @description End time of office leave (DateTime format)
             * @example 17:00:00
             */
            endTime: string;
            /**
             * Format: date-time
             * @description Date of office leave (Date format)
             * @example 2025-01-20
             */
            date: string;
            /**
             * @description Whether this is official business (categorized by HR)
             * @example true
             */
            isOfficialBusiness: boolean | null;
            /**
             * @description ID of HR who reviewed the office leave
             * @example hr_manager_123
             */
            reviewedBy: string | null;
            /**
             * Format: date-time
             * @description When the office leave was reviewed
             * @example 2025-01-19T14:30:00Z
             */
            reviewedAt: string | null;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        OfficeLeaveResponseDTO: components["schemas"]["OfficeLeave"] & {
            userName: string;
            userEmail: string;
            reviewerName: string | null;
            reviewerEmail: string | null;
        };
        TreeNodeDTO: {
            /**
             * @description User full name
             * @example John Doe
             */
            name: string;
            attributes: {
                [key: string]: unknown;
            };
            children?: {
                [key: string]: unknown;
            }[];
        };
        GetTreeNodeResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: components["schemas"]["TreeNodeDTO"][];
        };
        Task: {
            /**
             * @description Unique identifier for the task
             * @example xEHrmF2czmf7joyscp5J2
             */
            id?: string | null;
            /**
             * @description Task name/title
             * @example Task 1
             */
            name: string;
            /**
             * @description ID of user who assigned the task
             * @example bXn1LnLm8GNqn2OKSE4db
             */
            assignerId: string;
            /**
             * @description ID of user who is assigned the task
             * @example zKcA_9qLYn3JVufR4Vg_x
             */
            assigneeId: string;
            /**
             * Format: date-time
             * @description Task deadline (DATEONLY format)
             * @example 2025-02-15
             */
            deadline: string;
            /**
             * @description Task description/details
             * @example Prepare and submit the monthly sales report for Q1
             */
            description: string;
            /**
             * @description Array of task document URLs with string format
             * @example ['/uploads/tasks/task-doc-123.pdf']
             */
            documentUrls: string | null;
            /**
             * @description Task rating score (0-100)
             * @example 85
             */
            ratingPoint: number | null;
            /**
             * @description Reason provided by the supervisor for rejecting the task
             * @example There is some reason
             */
            rejectionReason: string | null;
            /**
             * @description General feedback from the supervisor for the task
             * @example Overall good effort, but need to fix formatting on page 5.
             */
            finalFeedback: string | null;
            /**
             * @description Overall task status (PENDING, IN_PROGRESS, IN_REVIEW, REVISION_REQUIRED, COMPLETED, REJECTED)
             * @example PENDING
             * @enum {string}
             */
            status: "COMPLETED" | "REJECTED" | "PENDING" | "IN_PROGRESS" | "IN_REVIEW" | "REVISION_REQUIRED";
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        User: {
            /**
             * @description Unique identifier for the user
             * @example user_bXn1LnLm8GNqn2OKSE4db
             */
            id: string;
            /**
             * @description Full name of the user
             * @example John Doe
             */
            name: string;
            /**
             * Format: email
             * @description Email address of the user
             * @example <EMAIL>
             */
            email: string;
            /**
             * @description Employee ID/NIK
             * @example EMP001
             */
            nik: string;
            /**
             * @description Mobile phone number
             * @example 08123456789
             */
            mobileNumber: string;
            /**
             * @description Whether email is verified
             * @example true
             */
            emailVerified: boolean;
            /**
             * @description User profile image URL
             * @example /uploads/avatars/user-avatar-123.jpg
             */
            image: string;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        ViolationTypeDTO: {
            /**
             * @description Unique identifier for the violation type
             * @example vt_xEHrmF2czmf7joyscp5J2
             */
            id: string;
            /**
             * @description Violation type name
             * @example Terlambat Masuk Kerja
             */
            name: string;
            /**
             * @description Violation type description
             * @example Pelanggaran ketika karyawan terlambat masuk kerja lebih dari 15 menit
             */
            description: string | null;
            /**
             * @description Penalty points for this violation type
             * @example 10
             */
            penaltyPoints: number;
            /**
             * @description Punishment
             * @example SP3
             */
            punishment: string;
            /**
             * @description Whether this violation type is active
             * @example true
             */
            isActive: boolean;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        ViolationDTO: {
            /**
             * @description Unique identifier for the violation
             * @example v_bXn1LnLm8GNqn2OKSE4db
             */
            id: string;
            /**
             * @description ID of the user who committed the violation
             * @example user_rJGI0UrX29J2pJN5lj9vN
             */
            userId: string;
            /**
             * @description ID of the violation type
             * @example vt_xEHrmF2czmf7joyscp5J2
             */
            violationTypeId: string;
            /**
             * Format: date-time
             * @description Date when violation occurred (YYYY-MM-DD format)
             * @example 2025-01-15
             */
            violationDate: string;
            /**
             * @description Additional notes about the violation
             * @example Terlambat 30 menit tanpa keterangan yang jelas
             */
            notes: string | null;
            /**
             * @description ID of the admin who recorded the violation
             * @example admin_Orx3CcXbu9m9xyjne6bbB
             */
            recordedBy: string;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        CreateViolationResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: components["schemas"]["ViolationDTO"];
        };
        CreateViolationDTO: {
            /**
             * @description ID of the user who committed the violat.ion
             * @example user123
             */
            userId: string;
            /**
             * @description ID of the violation type
             * @example violation-type-123
             */
            violationTypeId: string;
            /**
             * Format: date
             * @description Date when violation occurred (YYYY-MM-DD format)
             * @example 2024-01-15
             */
            violationDate: string;
            /**
             * @description Additional notes about the violation
             * @example Terlambat 30 menit tanpa keterangan
             */
            notes?: string;
        };
        GetMyViolationListPointResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: {
                violations: (components["schemas"]["ViolationDTO"] & {
                    /**
                     * @description Penalty point for violation
                     * @example 10
                     */
                    penaltyPoints: number;
                    /**
                     * @description Violation type/category name
                     * @example Pelangaran Kategori A
                     */
                    name: string;
                    /** @description Description of the violation type/category */
                    description: string | null;
                    /**
                     * @description Punishment of violation
                     * @example SP3
                     */
                    punishment: string;
                })[];
            };
        };
        GetViolationTypeDetailResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: components["schemas"]["ViolationTypeDTO"];
        };
        GetViolationTypeResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: {
                violationTypes: components["schemas"]["ViolationTypeDTO"][];
            };
        };
        AssignUserToWorksitesResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data?: unknown;
        };
        AssignUserToWorkSitesDTO: {
            /**
             * @description ID of the user to assign to work sites
             * @example Qah4cccEK0MsspUJ9ywAy
             */
            userId: string;
            /** @description Array of work site assignments */
            worksites: {
                /**
                 * @description Work site ID
                 * @example Qah4cccEK0MsspUJ9ywAy
                 */
                id: string;
                /**
                 * @description Whether this is the main work site for the user
                 * @example true
                 */
                isMain?: boolean | null;
            }[];
        };
        Worksite: {
            /**
             * @description Work site ID
             * @example Qah4cccEK0MsspUJ9ywAy
             */
            id: string;
            /**
             * @description Name of the work site
             * @example Jakarta Office
             */
            name: string;
            /**
             * @description Type of work site
             * @example Office
             */
            type: string;
            /**
             * @description Full address of the work site
             * @example Jl. Sudirman No. 123, Jakarta Pusat
             */
            address: string;
            /**
             * @description Optional description of the work site
             * @example Main office building
             */
            description: string | null;
            /**
             * Format: date-time
             * @description Creation timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update timestamp
             * @example 2025-01-15T08:30:15.000Z
             */
            updatedAt: string;
        };
        CreateWorksiteResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: components["schemas"]["Worksite"];
        };
        CreateWorkSiteDTO: {
            /**
             * @description Name of the work site
             * @example Jakarta Office
             */
            name: string;
            /**
             * @description Type of work site
             * @example Office
             */
            type: string;
            /**
             * @description Full address of the work site
             * @example Jl. Sudirman No. 123, Jakarta Pusat
             */
            address: string;
            /**
             * @description Optional description of the work site
             * @example Main office building
             */
            description?: string;
        };
        GetAttendanceRuleByWorksiteResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: components["schemas"]["AttendanceRule"];
        };
        GetWorksiteOptionsResponseDTO: {
            /**
             * @description Result status of the API call
             * @example success
             * @enum {string}
             */
            status: "success";
            /**
             * @description Descriptive message for the API result
             * @example Operation completed successfully.
             */
            message: string;
            data: {
                label: string;
                value: string | number;
            }[];
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
