import { nanoid } from "nanoid";
import IconsaxBriefCaseCrossIcon from "@/assets/icons/iconsax-briefcase-cross.svg?react";
import IconsaxBriefCaseTimerIcon from "@/assets/icons/iconsax-briefcase-timer.svg?react";
import IconsaxClipboardTextIcon from "@/assets/icons/iconsax-clipboard-text.svg?react";
import IconsaxDashboardIcon from "@/assets/icons/iconsax-dashboard.svg?react";
import IconsaxFavoriteChartIcon from "@/assets/icons/iconsax-favorite-chart.svg?react";
import IconsaxNoteRemoveIcon from "@/assets/icons/iconsax-note-remove.svg?react";
import IconsaxPersonalCardIcon from "@/assets/icons/iconsax-personal-card.svg?react";
import IconsaxSecurityTimerIcon from "@/assets/icons/iconsax-security-timer.svg?react";
import IconsaxTimerIcon from "@/assets/icons/iconsax-timer.svg?react";
import IconsaxTimerPauseIcon from "@/assets/icons/iconsax-timer-pause.svg?react";
import IconsaxUserIcon from "@/assets/icons/iconsax-user.svg?react";
import IconsaxUserGroupIcon from "@/assets/icons/iconsax-user-group.svg?react";
import IconsaxWarning2Icon from "@/assets/icons/iconsax-warning-2.svg?react";
import { IconWrapper } from "../../common/IconWrapper";
import type { NavItem } from "@/shared/types/common";

export const dashboardNavLists = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxDashboardIcon} />,
		text: "Dashboard",
		path: "/dashboard",
	},
];

export const userNavLists: NavItem[] = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxUserIcon} />,
		text: "Manajemen User",
		path: "/users",
		children: [
			{
				id: nanoid(),
				text: "List User",
				path: "/users",
			},
			{
				id: nanoid(),
				text: "List Admin",
				path: "/users/administrators",
			},
			{
				id: nanoid(),
				text: "Hierarki User",
				path: "/users/hierarchy",
			},
		],
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxUserGroupIcon} />,
		text: "Manajemen Organisasi",
		path: "/organizations",
		children: [
			{
				id: nanoid(),
				text: "List Jabatan",
				path: "/organizations",
			},
			{
				id: nanoid(),
				text: "Hierarki Jabatan",
				path: "/organizations/hierarchy",
			},
		],
	},
];

export const attendanceNavLists = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxTimerIcon} />,
		text: "Manajemen Absensi",
		path: "/attendance",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxSecurityTimerIcon} />,
		text: "Konfigurasi Absensi",
		path: "/attendance/configurations",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxTimerPauseIcon} />,
		text: "Manajemen Izin Keluar",
		path: "/office-leaves",
	},
];

export const leaveNavLists = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxBriefCaseCrossIcon} />,
		text: "Manajemen Cuti",
		path: "/leave",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxBriefCaseTimerIcon} />,
		text: "Konfigurasi Cuti",
		path: "/leave/configurations",
	},
];

export const taskNavLists = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxClipboardTextIcon} />,
		text: "Management Tugas",
		path: "/tasks",
	},
];

export const performanceNavLists = [
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxPersonalCardIcon} />,
		text: "Management Performa",
		path: "/performance",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxFavoriteChartIcon} />,
		text: "Management Reward",
		path: "/reward",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxNoteRemoveIcon} />,
		text: "Management Pelanggaran",
		path: "/violations",
	},
	{
		id: nanoid(),
		icon: <IconWrapper icon={IconsaxWarning2Icon} />,
		text: "Konfigurasi Pelanggaran",
		path: "/violations/configurations",
	},
];
